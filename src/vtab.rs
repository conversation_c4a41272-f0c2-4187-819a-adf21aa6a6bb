use duckdb::core::{<PERSON>C<PERSON>k<PERSON><PERSON>le, LogicalTypeId};
use duckdb::vtab::{BindInfo, InitInfo, VTab};
use duckdb::{ffi, Connection};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;

use crate::types::{JsonSchema, SchemaConfig};
use crate::schema::infer_schema_from_file;
use crate::duckdb_types::generate_column_definitions;
use crate::streaming_parser::StreamingJsonParser;
use crate::vector_writer::VectorWriter;
use crate::projection::ProjectionInfo;

#[repr(C)]
pub struct JsonReaderBindData {
    file_path: String,
    schema: JsonSchema,
    projection: ProjectionInfo,
}

#[repr(C)]
pub struct JsonReaderInitData {
    parser: std::sync::Mutex<Option<StreamingJsonParser>>,
    writer: VectorWrite<PERSON>,
    finished: std::sync::atomic::AtomicBool,
}

pub struct JsonReaderVTab;

impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> duckdb::Result<Self::BindData, Box<dyn std::error::Error>> {
        // Check if we have parameters
        if bind.get_parameter_count() == 0 {
            return Err("streaming_json_reader requires a file path parameter".into());
        }

        // Get file path from first parameter
        let file_path = bind.get_parameter(0).to_string();

        // Perform schema inference
        let config = SchemaConfig::default();
        let schema = infer_schema_from_file(&file_path, &config)
            .map_err(|e| format!("Schema inference failed: {}", e))?;

        // Generate column definitions based on schema
        let column_definitions = generate_column_definitions(&schema.root_type, &schema.processing_mode)
            .map_err(|e| format!("Column generation failed: {}", e))?;

        // Create projection info - for now, project all columns
        let projection = ProjectionInfo::full_projection(column_definitions.len());

        // Add columns to the table function
        for (column_name, logical_type) in column_definitions {
            bind.add_result_column(&column_name, logical_type);
        }

        eprintln!("JSON reader bind complete for {}", file_path);
        eprintln!("Schema: {:?}", schema);

        Ok(Self::BindData {
            file_path,
            schema,
            projection,
        })
    }

    fn init(_: &InitInfo) -> duckdb::Result<Self::InitData, Box<dyn std::error::Error>> {
        Ok(Self::InitData {
            parser: std::sync::Mutex::new(None),
            writer: VectorWriter::new(crate::types::ProcessingMode::SingleObject), // Will be updated in func
            finished: std::sync::atomic::AtomicBool::new(false),
        })
    }

    fn func(
        info: &duckdb::vtab::TableFunctionInfo<Self>,
        output: &mut DataChunkHandle,
    ) -> duckdb::Result<(), Box<dyn std::error::Error>> {
        let init = info.get_init_data();
        let bind = info.get_bind_data();

        // Lock the parser mutex
        let mut parser_guard = init.parser.lock().unwrap();

        // Initialize parser on first call
        if parser_guard.is_none() {
            let parser = StreamingJsonParser::new(&bind.file_path, bind.schema.clone())?;
            *parser_guard = Some(parser);
        }

        // Get the next chunk from the parser
        if let Some(ref mut parser) = parser_guard.as_mut() {
            if let Some(chunk) = parser.next_chunk()? {
                // Create a writer with the correct processing mode
                let writer = VectorWriter::new(bind.schema.processing_mode.clone());

                // Write the chunk to DuckDB vectors
                writer.write_chunk(&chunk, output, &bind.schema.root_type)?;

                eprintln!("Processed {} rows from {}", chunk.row_count, bind.file_path);

                if chunk.is_last {
                    // This was the last chunk, clear the parser BEFORE returning
                    *parser_guard = None;
                }

                return Ok(());
            } else {
                // Parser returned None, meaning no more data - clear it
                *parser_guard = None;
            }
        }

        // No more data - return 0 rows to signal completion to DuckDB
        output.set_len(0);
        Ok(())
    }

    fn parameters() -> Option<Vec<duckdb::core::LogicalTypeHandle>> {
        Some(vec![duckdb::core::LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

#[duckdb_entrypoint_c_api()]
pub unsafe fn streaming_json_reader_init(db: duckdb::Connection) -> duckdb::Result<(), Box<dyn std::error::Error>> {
    db.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .map_err(|e| format!("Failed to register table function: {}", e))?;

    eprintln!("Extension loaded successfully - table function registered");
    Ok(())
}