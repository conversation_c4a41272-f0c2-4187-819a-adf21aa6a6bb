use duckdb::core::{<PERSON><PERSON><PERSON>k<PERSON><PERSON><PERSON>, LogicalTypeId};
use duckdb::vtab::{BindInfo, InitInfo, VTab};
use duckdb::{ffi, Connection};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;

#[repr(C)]
pub struct JsonReaderBindData {
    file_path: String,
}

#[repr(C)]
pub struct JsonReaderInitData {
    finished: std::sync::atomic::AtomicBool,
}

pub struct JsonReaderVTab;

impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> duckdb::Result<Self::BindData, Box<dyn std::error::Error>> {
        // Check if we have parameters
        if bind.get_parameter_count() == 0 {
            return Err("streaming_json_reader requires a file path parameter".into());
        }

        // Get file path from first parameter
        let file_path = bind.get_parameter(0).to_string();

        // TODO: Implement new schema inference and column setup based on target design

        // For now, add a placeholder column to make the function work
        bind.add_result_column("placeholder", duckdb::core::LogicalTypeHandle::from(LogicalTypeId::Varchar));

        eprintln!("JSON reader bind complete for {}", file_path);

        Ok(Self::BindData {
            file_path,
        })
    }

    fn init(_: &InitInfo) -> duckdb::Result<Self::InitData, Box<dyn std::error::Error>> {
        Ok(Self::InitData {
            finished: std::sync::atomic::AtomicBool::new(false),
        })
    }

    fn func(
        info: &duckdb::vtab::TableFunctionInfo<Self>,
        output: &mut DataChunkHandle,
    ) -> duckdb::Result<(), Box<dyn std::error::Error>> {
        use std::sync::atomic::Ordering;

        let init = info.get_init_data();
        let bind = info.get_bind_data();

        // Check if we've already processed the file
        if init.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        // TODO: Implement new JSON processing based on target design
        eprintln!("Processing JSON file: {}", bind.file_path);

        // For now, return empty result
        output.set_len(0);

        // Mark as finished
        init.finished.store(true, Ordering::Relaxed);

        Ok(())
    }

    fn parameters() -> Option<Vec<duckdb::core::LogicalTypeHandle>> {
        Some(vec![duckdb::core::LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

#[duckdb_entrypoint_c_api()]
pub unsafe fn streaming_json_reader_init(db: duckdb::Connection) -> duckdb::Result<(), Box<dyn std::error::Error>> {
    db.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .map_err(|e| format!("Failed to register table function: {}", e))?;

    eprintln!("Extension loaded successfully - table function registered");
    Ok(())
}