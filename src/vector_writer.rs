use duckdb::core::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FlatVector, Inserter};
use crate::streaming_parser::{JsonValue, JsonChunk};
use crate::types::{JsonType, ProcessingMode};

/// Writer for populating DuckDB vectors with JSON data
pub struct VectorWriter {
    processing_mode: ProcessingMode,
}

impl VectorWriter {
    pub fn new(processing_mode: ProcessingMode) -> Self {
        Self { processing_mode }
    }

    /// Write a JSON chunk to DuckDB vectors
    pub fn write_chunk(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match &self.processing_mode {
            ProcessingMode::SingleObject => self.write_single_object(chunk, output, schema),
            ProcessingMode::ArrayOfObjects => self.write_array_of_objects(chunk, output, schema),
            ProcessingMode::ArrayOfPrimitives => self.write_array_of_primitives(chunk, output, schema),
            ProcessingMode::SinglePrimitive => self.write_single_primitive(chunk, output, schema),
        }
    }

    /// Write a single object to DuckDB vectors
    fn write_single_object(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let JsonType::Object { fields } = schema {
            if let Some(JsonValue::Object(obj_fields)) = chunk.values.first() {
                // Create a map for quick field lookup
                let field_map: std::collections::HashMap<&String, &JsonValue> = obj_fields.iter().map(|(k, v)| (k, v)).collect();

                for (col_idx, (field_name, _field_type)) in fields.iter().enumerate() {
                    let mut vector = output.flat_vector(col_idx);

                    if let Some(field_value) = field_map.get(field_name) {
                        self.write_simple_value_to_vector(field_value, &mut vector, 0)?;
                    }
                    // Note: For now, we don't handle NULL values explicitly
                    // The DuckDB API will handle this automatically
                }
            }
        }

        output.set_len(1);
        Ok(())
    }

    /// Write an array of objects to DuckDB vectors
    fn write_array_of_objects(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let JsonType::Array { element_type } = schema {
            if let JsonType::Object { fields } = element_type.as_ref() {
                // Process each row
                for (row_idx, value) in chunk.values.iter().enumerate() {
                    if let JsonValue::Object(obj_fields) = value {
                        let field_map: std::collections::HashMap<&String, &JsonValue> = obj_fields.iter().map(|(k, v)| (k, v)).collect();

                        for (col_idx, (field_name, _field_type)) in fields.iter().enumerate() {
                            let mut vector = output.flat_vector(col_idx);

                            if let Some(field_value) = field_map.get(field_name) {
                                self.write_simple_value_to_vector(field_value, &mut vector, row_idx)?;
                            }
                            // Note: For now, we don't handle NULL values explicitly
                        }
                    }
                }
            }
        }

        output.set_len(chunk.row_count);
        Ok(())
    }

    /// Write an array of primitives to DuckDB vectors
    fn write_array_of_primitives(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Check if the "primitive" is actually a LIST type
        if let JsonType::Array { element_type } = schema {
            match element_type.as_ref() {
                JsonType::Array { .. } => {
                    // This is an array of arrays - use LIST vector
                    return self.write_array_of_lists(chunk, output, element_type);
                }
                JsonType::Object { .. } => {
                    // This is an array of objects - this should not happen in ArrayOfPrimitives mode
                    return Err("Array of objects should be handled by ArrayOfObjects processing mode, not ArrayOfPrimitives".into());
                }
                _ => {
                    // This is truly an array of primitives
                    let mut vector = output.flat_vector(0); // Single "value" column
                    for (row_idx, value) in chunk.values.iter().enumerate() {
                        self.write_simple_value_to_vector(value, &mut vector, row_idx)?;
                    }
                }
            }
        }

        output.set_len(chunk.row_count);
        Ok(())
    }

    /// Write a single primitive to DuckDB vectors
    fn write_single_primitive(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        _schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(value) = chunk.values.first() {
            let mut vector = output.flat_vector(0); // Single "value" column
            self.write_simple_value_to_vector(value, &mut vector, 0)?;
        }

        output.set_len(1);
        Ok(())
    }

    /// Write an array of lists to DuckDB LIST vectors
    fn write_array_of_lists(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        element_type: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let mut list_vector = output.list_vector(0); // Single "value" column as LIST

        let mut total_child_elements = 0;

        // First pass: count total child elements and set list entries
        for (row_idx, value) in chunk.values.iter().enumerate() {
            match value {
                JsonValue::Array(arr) => {
                    let list_length = arr.len();
                    list_vector.set_entry(row_idx, total_child_elements, list_length);
                    total_child_elements += list_length;
                }
                JsonValue::Null => {
                    list_vector.set_null(row_idx);
                    list_vector.set_entry(row_idx, total_child_elements, 0);
                }
                _ => {
                    return Err(format!("Expected array value in array of lists, got: {:?}", value).into());
                }
            }
        }

        // Get the child vector for the list elements
        if let JsonType::Array { element_type: child_element_type } = element_type {
            match child_element_type.as_ref() {
                JsonType::Array { .. } => {
                    // Child elements are also arrays - need nested LIST vector
                    let mut child_list_vector = list_vector.list_child();
                    self.write_nested_list_elements(chunk, &mut child_list_vector, child_element_type)?;
                }
                _ => {
                    // Child elements are primitives - use flat vector
                    let mut child_vector = list_vector.child(total_child_elements);

                    // Second pass: populate child elements
                    let mut child_idx = 0;
                    for value in chunk.values.iter() {
                        if let JsonValue::Array(arr) = value {
                            for child_value in arr {
                                self.write_simple_value_to_vector(child_value, &mut child_vector, child_idx)?;
                                child_idx += 1;
                            }
                        }
                    }
                }
            }
        }

        output.set_len(chunk.row_count);
        Ok(())
    }

    /// Write a JSON value to any DuckDB vector type (FlatVector, ListVector, etc.) based on the expected type
    fn write_value_to_vector(
        &self,
        value: &JsonValue,
        vector: &mut FlatVector,
        row_idx: usize,
        expected_type: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match expected_type {
            JsonType::Array { element_type } => {
                // This should be a LIST vector, but we received a FlatVector
                // We need to handle this case by converting the FlatVector to a ListVector
                return Err("Cannot write array value to FlatVector - need ListVector".into());
            }
            JsonType::Object { .. } => {
                // This should be a STRUCT vector
                return Err("Cannot write object value to FlatVector - need StructVector".into());
            }
            _ => {
                // This is a primitive type, use the simple writer
                self.write_simple_value_to_vector(value, vector, row_idx)
            }
        }
    }

    /// Write a JSON value to a DuckDB vector with proper type handling
    fn write_simple_value_to_vector(
        &self,
        value: &JsonValue,
        vector: &mut FlatVector,
        row_idx: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match value {
            JsonValue::Null => {
                // Handle NULL values properly
                vector.set_null(row_idx);
            }
            JsonValue::Boolean(b) => {
                // For boolean, we need to write to the underlying data slice
                vector.as_mut_slice::<bool>()[row_idx] = *b;
            }
            JsonValue::Integer(i) => {
                // For integers, write to i64 slice
                vector.as_mut_slice::<i64>()[row_idx] = *i;
            }
            JsonValue::Number(n) => {
                // For numbers, write to f64 slice
                vector.as_mut_slice::<f64>()[row_idx] = *n;
            }
            JsonValue::String(s) => {
                // For strings, use the Inserter trait
                vector.insert(row_idx, s.as_str());
            }
            JsonValue::Array(_arr) => {
                return Err("Array values require LIST vector handling - this should be handled by write_list_value_to_vector".into());
            }
            JsonValue::Object(_) => {
                return Err("Object values require STRUCT vector handling - use write_struct_value_to_vector instead".into());
            }
        }
        Ok(())
    }
}
