use duckdb::core::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FlatVector, Inserter};
use crate::streaming_parser::{JsonValue, JsonChunk};
use crate::types::{JsonType, ProcessingMode};

/// Writer for populating DuckDB vectors with JSON data
pub struct VectorWriter {
    processing_mode: ProcessingMode,
}

impl VectorWriter {
    pub fn new(processing_mode: ProcessingMode) -> Self {
        Self { processing_mode }
    }

    /// Write a JSON chunk to DuckDB vectors
    pub fn write_chunk(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match &self.processing_mode {
            ProcessingMode::SingleObject => self.write_single_object(chunk, output, schema),
            ProcessingMode::ArrayOfObjects => self.write_array_of_objects(chunk, output, schema),
            ProcessingMode::ArrayOfPrimitives => self.write_array_of_primitives(chunk, output, schema),
            ProcessingMode::SinglePrimitive => self.write_single_primitive(chunk, output, schema),
        }
    }

    /// Write a single object to DuckDB vectors
    fn write_single_object(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let JsonType::Object { fields } = schema {
            if let Some(JsonValue::Object(obj_fields)) = chunk.values.first() {
                // Create a map for quick field lookup
                let field_map: std::collections::HashMap<&String, &JsonValue> = obj_fields.iter().map(|(k, v)| (k, v)).collect();

                for (col_idx, (field_name, _field_type)) in fields.iter().enumerate() {
                    let vector = output.flat_vector(col_idx);

                    if let Some(field_value) = field_map.get(field_name) {
                        self.write_simple_value_to_vector(field_value, &vector, 0)?;
                    }
                    // Note: For now, we don't handle NULL values explicitly
                    // The DuckDB API will handle this automatically
                }
            }
        }

        output.set_len(1);
        Ok(())
    }

    /// Write an array of objects to DuckDB vectors
    fn write_array_of_objects(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let JsonType::Array { element_type } = schema {
            if let JsonType::Object { fields } = element_type.as_ref() {
                // Process each row
                for (row_idx, value) in chunk.values.iter().enumerate() {
                    if let JsonValue::Object(obj_fields) = value {
                        let field_map: std::collections::HashMap<&String, &JsonValue> = obj_fields.iter().map(|(k, v)| (k, v)).collect();

                        for (col_idx, (field_name, _field_type)) in fields.iter().enumerate() {
                            let vector = output.flat_vector(col_idx);

                            if let Some(field_value) = field_map.get(field_name) {
                                self.write_simple_value_to_vector(field_value, &vector, row_idx)?;
                            }
                            // Note: For now, we don't handle NULL values explicitly
                        }
                    }
                }
            }
        }

        output.set_len(chunk.row_count);
        Ok(())
    }

    /// Write an array of primitives to DuckDB vectors
    fn write_array_of_primitives(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        _schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let vector = output.flat_vector(0); // Single "value" column

        for (row_idx, value) in chunk.values.iter().enumerate() {
            self.write_simple_value_to_vector(value, &vector, row_idx)?;
        }

        output.set_len(chunk.row_count);
        Ok(())
    }

    /// Write a single primitive to DuckDB vectors
    fn write_single_primitive(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        _schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(value) = chunk.values.first() {
            let vector = output.flat_vector(0); // Single "value" column
            self.write_simple_value_to_vector(value, &vector, 0)?;
        }

        output.set_len(1);
        Ok(())
    }

    /// Write a simple JSON value to a DuckDB vector (simplified implementation)
    fn write_simple_value_to_vector(
        &self,
        value: &JsonValue,
        vector: &FlatVector,
        row_idx: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // For now, convert everything to string as a fallback
        // This is a simplified implementation to get the basic structure working
        let string_value = match value {
            JsonValue::Null => "null".to_string(),
            JsonValue::Boolean(b) => b.to_string(),
            JsonValue::Integer(i) => i.to_string(),
            JsonValue::Number(n) => n.to_string(),
            JsonValue::String(s) => s.clone(),
            JsonValue::Array(_) => "[array]".to_string(), // Placeholder
            JsonValue::Object(_) => "{object}".to_string(), // Placeholder
        };

        // Use the Inserter trait to insert the string value
        vector.insert(row_idx, string_value.as_str());
        Ok(())
    }
}
