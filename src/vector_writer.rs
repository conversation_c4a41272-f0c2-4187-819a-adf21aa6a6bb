use duckdb::core::{<PERSON><PERSON><PERSON>k<PERSON><PERSON><PERSON>, FlatVector, ListVector, StructVector};
use duckdb::ffi::{
    duckdb_list_entry, duckdb_list_vector_get_child, duckdb_list_vector_get_size,
    duckdb_list_vector_set_size, duckdb_struct_vector_get_child, duckdb_validity_set_row_invalid,
    duckdb_vector_get_data, duckdb_vector_get_validity, duckdb_vector_assign_string_element_len,
};
use crate::streaming_parser::{JsonValue, JsonChunk};
use crate::types::{JsonType, ProcessingMode};

/// Writer for populating DuckDB vectors with JSON data
pub struct VectorWriter {
    processing_mode: ProcessingMode,
}

impl VectorWriter {
    pub fn new(processing_mode: ProcessingMode) -> Self {
        Self { processing_mode }
    }

    /// Write a JSON chunk to DuckDB vectors
    pub fn write_chunk(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match &self.processing_mode {
            ProcessingMode::SingleObject => self.write_single_object(chunk, output, schema),
            ProcessingMode::ArrayOfObjects => self.write_array_of_objects(chunk, output, schema),
            ProcessingMode::ArrayOfPrimitives => self.write_array_of_primitives(chunk, output, schema),
            ProcessingMode::SinglePrimitive => self.write_single_primitive(chunk, output, schema),
        }
    }

    /// Write a single object to DuckDB vectors
    fn write_single_object(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let JsonType::Object { fields } = schema {
            if let Some(JsonValue::Object(obj_fields)) = chunk.values.first() {
                // Create a map for quick field lookup
                let field_map: std::collections::HashMap<&String, &JsonValue> = obj_fields.iter().map(|(k, v)| (k, v)).collect();

                for (col_idx, (field_name, field_type)) in fields.iter().enumerate() {
                    let vector = output.flat_vector(col_idx);
                    
                    if let Some(field_value) = field_map.get(field_name) {
                        self.write_value_to_vector(field_value, &vector, 0, field_type)?;
                    } else {
                        // Field is missing - set as NULL
                        self.set_vector_null(&vector, 0)?;
                    }
                }
            }
        }
        
        output.set_len(1);
        Ok(())
    }

    /// Write an array of objects to DuckDB vectors
    fn write_array_of_objects(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let JsonType::Array { element_type } = schema {
            if let JsonType::Object { fields } = element_type.as_ref() {
                // Process each row
                for (row_idx, value) in chunk.values.iter().enumerate() {
                    if let JsonValue::Object(obj_fields) = value {
                        let field_map: std::collections::HashMap<&String, &JsonValue> = obj_fields.iter().map(|(k, v)| (k, v)).collect();

                        for (col_idx, (field_name, field_type)) in fields.iter().enumerate() {
                            let vector = output.flat_vector(col_idx);
                            
                            if let Some(field_value) = field_map.get(field_name) {
                                self.write_value_to_vector(field_value, &vector, row_idx, field_type)?;
                            } else {
                                // Field is missing - set as NULL
                                self.set_vector_null(&vector, row_idx)?;
                            }
                        }
                    }
                }
            }
        }
        
        output.set_len(chunk.row_count);
        Ok(())
    }

    /// Write an array of primitives to DuckDB vectors
    fn write_array_of_primitives(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let JsonType::Array { element_type } = schema {
            let vector = output.flat_vector(0); // Single "value" column
            
            for (row_idx, value) in chunk.values.iter().enumerate() {
                self.write_value_to_vector(value, &vector, row_idx, element_type)?;
            }
        }
        
        output.set_len(chunk.row_count);
        Ok(())
    }

    /// Write a single primitive to DuckDB vectors
    fn write_single_primitive(
        &self,
        chunk: &JsonChunk,
        output: &mut DataChunkHandle,
        schema: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(value) = chunk.values.first() {
            let vector = output.flat_vector(0); // Single "value" column
            self.write_value_to_vector(value, &vector, 0, schema)?;
        }
        
        output.set_len(1);
        Ok(())
    }

    /// Write a JSON value to a specific position in a DuckDB vector
    fn write_value_to_vector(
        &self,
        value: &JsonValue,
        vector: &FlatVector,
        row_idx: usize,
        expected_type: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match (value, expected_type) {
            (JsonValue::Null, _) => {
                self.set_vector_null(vector, row_idx)?;
            }
            (JsonValue::Boolean(b), JsonType::Boolean) => {
                self.write_boolean_to_vector(vector, row_idx, *b)?;
            }
            (JsonValue::Integer(i), JsonType::Integer) => {
                self.write_integer_to_vector(vector, row_idx, *i)?;
            }
            (JsonValue::Number(n), JsonType::Number) => {
                self.write_number_to_vector(vector, row_idx, *n)?;
            }
            (JsonValue::String(s), JsonType::String) => {
                self.write_string_to_vector(vector, row_idx, s)?;
            }
            (JsonValue::Array(arr), JsonType::Array { element_type }) => {
                self.write_array_to_vector(vector, row_idx, arr, element_type)?;
            }
            (JsonValue::Object(obj), JsonType::Object { fields }) => {
                self.write_object_to_vector(vector, row_idx, obj, fields)?;
            }
            _ => {
                return Err(format!("Type mismatch: cannot write {:?} to {:?}", value, expected_type).into());
            }
        }
        Ok(())
    }

    /// Set a vector position to NULL
    fn set_vector_null(&self, vector: &FlatVector, row_idx: usize) -> Result<(), Box<dyn std::error::Error>> {
        unsafe {
            duckdb_validity_set_row_invalid(
                duckdb_vector_get_validity(vector.ptr),
                row_idx as u64,
            );
        }
        Ok(())
    }

    /// Write a boolean value to a vector
    fn write_boolean_to_vector(
        &self,
        vector: &FlatVector,
        row_idx: usize,
        value: bool,
    ) -> Result<(), Box<dyn std::error::Error>> {
        unsafe {
            let data = duckdb_vector_get_data(vector.ptr) as *mut bool;
            *data.add(row_idx) = value;
        }
        Ok(())
    }

    /// Write an integer value to a vector
    fn write_integer_to_vector(
        &self,
        vector: &FlatVector,
        row_idx: usize,
        value: i64,
    ) -> Result<(), Box<dyn std::error::Error>> {
        unsafe {
            let data = duckdb_vector_get_data(vector.ptr) as *mut i64;
            *data.add(row_idx) = value;
        }
        Ok(())
    }

    /// Write a number value to a vector
    fn write_number_to_vector(
        &self,
        vector: &FlatVector,
        row_idx: usize,
        value: f64,
    ) -> Result<(), Box<dyn std::error::Error>> {
        unsafe {
            let data = duckdb_vector_get_data(vector.ptr) as *mut f64;
            *data.add(row_idx) = value;
        }
        Ok(())
    }

    /// Write a string value to a vector
    fn write_string_to_vector(
        &self,
        vector: &FlatVector,
        row_idx: usize,
        value: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        unsafe {
            duckdb_vector_assign_string_element_len(
                vector.ptr,
                row_idx as u64,
                value.as_ptr() as *const i8,
                value.len() as u64,
            );
        }
        Ok(())
    }

    /// Write an array to a list vector
    fn write_array_to_vector(
        &self,
        vector: &FlatVector,
        row_idx: usize,
        array: &[JsonValue],
        element_type: &JsonType,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let list_vector = ListVector::from(vector.ptr);
        
        unsafe {
            // Get the child vector for list elements
            let child_vector = FlatVector::from(duckdb_list_vector_get_child(vector.ptr));
            
            // Get current child vector size
            let current_size = duckdb_list_vector_get_size(vector.ptr) as usize;
            
            // Set up list entry
            let list_data = duckdb_vector_get_data(vector.ptr) as *mut duckdb_list_entry;
            let list_entry = list_data.add(row_idx);
            (*list_entry).offset = current_size as u64;
            (*list_entry).length = array.len() as u64;
            
            // Write array elements to child vector
            for (elem_idx, element) in array.iter().enumerate() {
                let child_row_idx = current_size + elem_idx;
                self.write_value_to_vector(element, &child_vector, child_row_idx, element_type)?;
            }
            
            // Update child vector size
            let new_size = current_size + array.len();
            duckdb_list_vector_set_size(vector.ptr, new_size as u64);
        }
        
        Ok(())
    }

    /// Write an object to a struct vector
    fn write_object_to_vector(
        &self,
        vector: &FlatVector,
        row_idx: usize,
        object: &[(String, JsonValue)],
        fields: &[(String, JsonType)],
    ) -> Result<(), Box<dyn std::error::Error>> {
        let struct_vector = StructVector::from(vector.ptr);
        
        // Create a map for quick field lookup
        let field_map: std::collections::HashMap<&String, &JsonValue> = object.iter().map(|(k, v)| (k, v)).collect();
        
        for (field_idx, (field_name, field_type)) in fields.iter().enumerate() {
            unsafe {
                let child_vector = FlatVector::from(duckdb_struct_vector_get_child(vector.ptr, field_idx as u64));
                
                if let Some(field_value) = field_map.get(field_name) {
                    self.write_value_to_vector(field_value, &child_vector, row_idx, field_type)?;
                } else {
                    // Field is missing - set as NULL
                    self.set_vector_null(&child_vector, row_idx)?;
                }
            }
        }
        
        Ok(())
    }
}
